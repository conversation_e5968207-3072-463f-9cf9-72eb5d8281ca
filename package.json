{"name": "riz-app", "version": "1.0.0", "main": "node_modules/expo/AppEntry.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web"}, "dependencies": {"@expo/metro-runtime": "~4.0.1", "@expo/vector-icons": "^14.1.0", "@react-native-async-storage/async-storage": "1.23.1", "@react-native-firebase/analytics": "^21.14.0", "@react-native-firebase/crashlytics": "^21.14.0", "@react-native-firebase/messaging": "^21.14.0", "@react-native-picker/picker": "^2.9.0", "@react-navigation/bottom-tabs": "^6.5.8", "@react-navigation/native": "^6.1.7", "@react-navigation/native-stack": "^6.9.13", "@react-navigation/stack": "^6.4.1", "@reduxjs/toolkit": "^1.9.5", "@sentry/react-native": "^6.10.0", "algoliasearch": "^4.24.0", "expo": "~52.0.0", "expo-auth-session": "~6.0.3", "expo-image-picker": "~16.0.6", "expo-localization": "~16.0.1", "expo-notifications": "~0.29.14", "expo-status-bar": "~2.0.1", "expo-updates": "^0.27.4", "i18next": "^23.5.0", "intl-pluralrules": "^2.0.1", "jimp": "^1.6.0", "jimp-compact": "^0.16.1-2", "react": "18.3.1", "react-dom": "18.3.1", "react-i18next": "^13.2.0", "react-native": "0.76.9", "react-native-dropdown-picker": "^5.4.6", "react-native-localize": "^3.0.6", "react-native-mmkv": "^3.2.0", "react-native-render-html": "^6.3.4", "react-native-safe-area-context": "4.12.0", "react-native-screens": "~4.4.0", "react-native-web": "~0.19.13", "react-redux": "^8.1.2"}, "devDependencies": {"@babel/core": "^7.20.0", "@types/jest": "^29.5.14", "@types/react": "~18.3.12", "babel-plugin-module-resolver": "^5.0.0", "typescript": "^5.1.6"}, "private": true, "overrides": {"jimp-compact": "^0.16.1-2"}}