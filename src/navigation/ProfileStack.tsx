import { createStackNavigator } from '@react-navigation/stack';
import ProfileScreenFromMain from '../screens/main/ProfileScreen'; // Updated import
import InstallmentsScreen from '../screens/profile/InstallmentsScreen';

// Define and export ParamList for ProfileStack
export type ProfileStackParamList = {
  ProfileDetails: undefined; // Route for ProfileScreenFromMain
  Installments: undefined;
};

const Stack = createStackNavigator<ProfileStackParamList>();

const ProfileStack = () => {
  return (
    <Stack.Navigator initialRouteName="ProfileDetails">
      <Stack.Screen
        name="ProfileDetails" // Updated name
        component={ProfileScreenFromMain}
        options={{ title: 'Profile' }} // You can customize the title
      />
      <Stack.Screen
        name="Installments"
        component={InstallmentsScreen}
        options={{ title: 'My Installments' }}
      />
    </Stack.Navigator>
  );
};

export default ProfileStack;
