import Constants from 'expo-constants';

interface AppConfig {
  apiUrl: string;
}

// Default configuration for development
const defaultConfig: AppConfig = {
  apiUrl: 'http://192.168.0.26:3000',
};

// Safely get configuration values
export const getConfig = (): AppConfig => {
  if (!Constants.manifest2?.extra) {
    console.warn('Configuration not found, using default values');
    return defaultConfig;
  }

  const { extra } = Constants.manifest2;

  return {
    apiUrl: extra.apiUrl || defaultConfig.apiUrl,
  };
};

// Singleton instance of the config
const config = getConfig();

export default config;