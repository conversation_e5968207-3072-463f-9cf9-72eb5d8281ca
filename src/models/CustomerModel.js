const { executeQuery, sql } = require('../config/database');

class CustomerModel {
  // Existing methods...

  static async getInstallmentsByPhone(phoneNumber) {
    try {
      // Input validation
      if (!phoneNumber) {
        throw new Error('Phone number is required');
      }

      // Sanitize input
      const sanitizedPhone = phoneNumber.toString().trim();

      // Validate format
      if (sanitizedPhone.length < 8) {
        throw new Error('Invalid phone number format');
      }

      // First get the customer's national ID using phone number
      const customerQuery = `
        SELECT TOP 1 ID1
        FROM mostafa3
        WHERE Mob1 = @phoneNumber
      `;

      const customerParams = [
        { name: 'phoneNumber', type: sql.NVarChar, value: sanitizedPhone }
      ];

      const customerResult = await executeQuery(customerQuery, customerParams);

      if (!customerResult || !customerResult.recordset || customerResult.recordset.length === 0) {
        return []; // No customer found with this phone number
      }

      const nationalId = customerResult.recordset[0].ID1;

      // Now get the installments using the national ID
      return await this.getInstallments(nationalId);
    } catch (error) {
      console.error('Error fetching installments by phone:', error);
      throw new Error('Failed to fetch installments by phone');
    }
  }
}

module.exports = CustomerModel;