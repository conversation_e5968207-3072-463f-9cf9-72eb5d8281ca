const CustomerModel = require('../models/CustomerModel');

class InstallmentsController {
  async getInstallmentsByPhone(req, res) {
    try {
      const { phoneNumber } = req.params;
      
      if (!phoneNumber) {
        return res.status(400).json({ 
          success: false, 
          message: 'Phone number is required' 
        });
      }

      const installments = await CustomerModel.getInstallmentsByPhone(phoneNumber);
      
      // Calculate summary data
      const totalDue = installments.reduce((sum, item) => sum + item.remainingAmount, 0);
      const totalPaid = installments.reduce((sum, item) => sum + item.paidAmount, 0);
      const overdueInstallments = installments.filter(item => item.lateDays > 0);
      
      return res.status(200).json({
        success: true,
        data: {
          installments,
          summary: {
            totalDue,
            totalPaid,
            totalInstallments: installments.length,
            overdueInstallments: overdueInstallments.length
          }
        }
      });
    } catch (error) {
      console.error('Error in getInstallmentsByPhone:', error);
      return res.status(500).json({
        success: false,
        message: 'Failed to fetch installments',
        error: error.message
      });
    }
  }
}

module.exports = new InstallmentsController();