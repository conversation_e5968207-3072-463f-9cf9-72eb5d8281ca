import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Cart, CartItem } from '../types/cart';
import { Product } from '../types/product';
import config from '../utils/config';
import type { RootState } from '../store';

interface CreditFormData {
  monthlyIncome: number;
  employmentStatus: string;
  employmentDuration: string;
}

interface PaymentIntent {
  clientSecret: string;
  redirect_url?: string;
  reserve_id?: string;
}

// Address type for customer endpoints
export interface CustomerAddress {
  address1: string;
  address2?: string;
  city: string;
  country: string;
  zip?: string;
  phone?: string;
}

// Helper function to get token from AsyncStorage
const getToken = async () => {
  try {
    return await AsyncStorage.getItem('token');
  } catch (error) {
    console.error('Error getting token:', error);
    return null;
  }
};

export const api = createApi({
  reducerPath: 'api',
  baseQuery: fetchBaseQuery({
    baseUrl: config.apiUrl,
    prepareHeaders: async (headers, api) => {
      // Prefer token from Redux state, fallback to AsyncStorage
      const state = api.getState() as RootState;
      let token = state?.auth?.token;
      if (!token) {
        token = await getToken();
      }
      if (token) {
        headers.set('authorization', `Bearer ${token}`);
      }
      console.log('[API DEBUG] Token used for Authorization header:', token);
      console.log('[API DEBUG] Headers before request:', headers);
      return headers;
    },
  }),
  endpoints: (builder) => ({
    // Cart endpoints
    getCart: builder.query<Cart, void>({
      query: () => 'cart',
    }),
    addToCart: builder.mutation<Cart, CartItem>({
      query: (item) => ({
        url: 'cart/items',
        method: 'POST',
        body: item,
      }),
    }),
    updateCartItem: builder.mutation<Cart, { id: string; quantity: number }>({
      query: ({ id, quantity }) => ({
        url: `cart/items/${id}`,
        method: 'PATCH',
        body: { quantity },
      }),
    }),
    removeFromCart: builder.mutation<Cart, string>({
      query: (id) => ({
        url: `cart/items/${id}`,
        method: 'DELETE',
      }),
    }),
    clearCart: builder.mutation<void, void>({
      query: () => ({
        url: 'cart',
        method: 'DELETE',
      }),
    }),

    // Customer address endpoints
    getCustomerAddress: builder.query<CustomerAddress | null, void>({
      query: () => 'customer/address',
    }),
    updateCustomerAddress: builder.mutation<{ success: boolean }, CustomerAddress>({
      query: (address) => ({
        url: 'customer/address',
        method: 'POST',
        body: address,
      }),
    }),

    // Credit endpoints
    submitCreditRequest: builder.mutation<{ status: string }, {
      national_id: string;
      monthly_income: number;
      employment_status: string;
      employer?: string;
      housing_status: string;
      consent_credit_check: boolean;
    }>({
      query: (data) => ({
        url: 'credit-request',
        method: 'POST',
        body: data,
      }),
    }),
    getCreditLimit: builder.query<{ limit: number }, void>({
      query: () => 'credit/limit',
    }),

    // Payment endpoints
    createPaymentIntent: builder.mutation<PaymentIntent, { amount: number; cartId: string }>({
      query: (data) => ({
        url: 'payments/intent',
        method: 'POST',
        body: data,
      }),
    }),
    // Auth endpoints
    authEmail: builder.mutation<
      { access: string; refresh: string; user: { id: string; email: string; phone: string } },
      { email: string; password: string; first_name: string; last_name: string; phone: string }
    >({
      query: (data) => ({
        url: 'auth/email',
        method: 'POST',
        body: data,
      }),
    }),
    authRefresh: builder.mutation<{ access: string }, { refresh: string }>({
      query: (data) => ({
        url: 'auth/refresh',
        method: 'POST',
        body: data,
      }),
    }),
    // Meta endpoint
    getMetaVersion: builder.query<{ ok: boolean; notice?: string }, void>({
      query: () => 'meta/version',
    }),
    // Collections endpoint
    getCollections: builder.query<any, void>({
      query: () => 'collections',
    }),
    getProductsByCollection: builder.query<any[], string>({
      query: (collectionId) => `collections/${collectionId}/products`,
    }),
    // Products endpoints
    getProducts: builder.query<Product[], { page?: number; limit?: number } | void>({
      query: (params) => {
        let url = 'products';
        if (params && (params.page || params.limit)) {
          const searchParams = [];
          if (params.page) searchParams.push(`page=${params.page}`);
          if (params.limit) searchParams.push(`limit=${params.limit}`);
          url += `?${searchParams.join('&')}`;
        }
        return url;
      },
    }),
    getProductById: builder.query<Product, string>({
      query: (id) => `products/${id}`,
    }),
    firebaseRegister: builder.mutation<any, { email: string; password: string; firstName: string; lastName: string; phone?: string }>({
      query: (body) => ({
        url: 'auth/firebase-register',
        method: 'POST',
        body,
      }),
    }),
    firebaseLogin: builder.mutation<any, { email: string; password: string }>({
      query: (body) => ({
        url: 'auth/firebase-login',
        method: 'POST',
        body,
      }),
    }),
    // User onboarding form endpoints (BFF will handle Firebase)
    submitUserOnboarding: builder.mutation<{ success: boolean }, any>({
      query: (data) => ({
        url: 'users/onboarding',
        method: 'POST',
        body: data,
      }),
    }),
    uploadUserAttachment: builder.mutation<{ url: string }, { userId: string; file: any; fileName: string }>({
      query: ({ userId, file, fileName }) => {
        const formData = new FormData();
        formData.append('file', file);
        formData.append('fileName', fileName);
        return {
          url: `users/${userId}/attachments`,
          method: 'POST',
          body: formData,
        };
      },
    }),
  placeOrder: builder.mutation<any, { customerId: string; cartId: string; paymentMethod: string }>({
    query: (data) => ({
      url: 'orders',
      method: 'POST',
      body: data,
    }),
  }),
  payInCash: builder.mutation<any, { customerId: string; cartId: string }>({
    query: (data) => ({
      url: 'orders/pay-in-cash',
      method: 'POST',
      body: data,
    }),
  }),
  getInstallments: builder.query<{
    installments: Array<{
      dueAmount: number;
      paidAmount: number;
      dueDate: string;
      isPaid: boolean;
      lateDays: number;
      remainingAmount: number;
    }>;
    summary: {
      totalDue: number;
      totalPaid: number;
      totalInstallments: number;
      overdueInstallments: number;
    };
  }, string>({
    query: (phoneNumber) => `/installments/${phoneNumber}`,
    transformResponse: (response: any) => response.data,
  }),
  }),
});

export const {
  useGetCartQuery,
  useAddToCartMutation,
  useUpdateCartItemMutation,
  useRemoveFromCartMutation,
  useClearCartMutation,
  useGetCustomerAddressQuery,
  useUpdateCustomerAddressMutation,
  useSubmitCreditRequestMutation,
  useGetCreditLimitQuery,
  useCreatePaymentIntentMutation,
  useAuthEmailMutation,
  useAuthRefreshMutation,
  useGetMetaVersionQuery,
  useGetCollectionsQuery,
  useGetProductsQuery,
  useGetProductByIdQuery,
  useGetProductsByCollectionQuery,
  useFirebaseRegisterMutation,
  useFirebaseLoginMutation,
  useSubmitUserOnboardingMutation,
  useUploadUserAttachmentMutation,
  usePlaceOrderMutation,
  usePayInCashMutation,
  useGetInstallmentsQuery,
} = api;
