import React from 'react';
import { View, Text, StyleSheet, FlatList, ActivityIndicator } from 'react-native';
import { useSelector } from 'react-redux';
import { useGetInstallmentsQuery } from '../../services/api';
import { RootState } from '../../store';
import { formatCurrency } from '../../utils/formatters';

const InstallmentsScreen = () => {
  const user = useSelector((state: RootState) => state.auth.user);
  const { data, isLoading, error } = useGetInstallmentsQuery(user?.phone || '');

  if (isLoading) {
    return (
      <View style={styles.centered}>
        <ActivityIndicator size="large" color="#0000ff" />
      </View>
    );
  }

  if (error) {
    return (
      <View style={styles.centered}>
        <Text style={styles.errorText}>Failed to load installments</Text>
      </View>
    );
  }

  if (!data || data.installments.length === 0) {
    return (
      <View style={styles.centered}>
        <Text>No installments found</Text>
      </View>
    );
  }

  const renderInstallmentItem = ({ item }: { item: any }) => {
    const dueDate = new Date(item.dueDate).toLocaleDateString();
    
    return (
      <View style={styles.installmentItem}>
        <View style={styles.row}>
          <Text style={styles.label}>Due Date:</Text>
          <Text style={styles.value}>{dueDate}</Text>
        </View>
        <View style={styles.row}>
          <Text style={styles.label}>Amount:</Text>
          <Text style={styles.value}>{formatCurrency(item.dueAmount)}</Text>
        </View>
        <View style={styles.row}>
          <Text style={styles.label}>Paid:</Text>
          <Text style={styles.value}>{formatCurrency(item.paidAmount)}</Text>
        </View>
        <View style={styles.row}>
          <Text style={styles.label}>Remaining:</Text>
          <Text style={[styles.value, item.remainingAmount > 0 ? styles.unpaid : styles.paid]}>
            {formatCurrency(item.remainingAmount)}
          </Text>
        </View>
        {item.lateDays > 0 && (
          <View style={styles.lateWarning}>
            <Text style={styles.lateText}>Late by {item.lateDays} days</Text>
          </View>
        )}
      </View>
    );
  };

  return (
    <View style={styles.container}>
      <View style={styles.summaryContainer}>
        <Text style={styles.summaryTitle}>Summary</Text>
        <View style={styles.summaryRow}>
          <Text style={styles.summaryLabel}>Total Due:</Text>
          <Text style={styles.summaryValue}>{formatCurrency(data.summary.totalDue)}</Text>
        </View>
        <View style={styles.summaryRow}>
          <Text style={styles.summaryLabel}>Total Paid:</Text>
          <Text style={styles.summaryValue}>{formatCurrency(data.summary.totalPaid)}</Text>
        </View>
        <View style={styles.summaryRow}>
          <Text style={styles.summaryLabel}>Installments:</Text>
          <Text style={styles.summaryValue}>{data.summary.totalInstallments}</Text>
        </View>
        {data.summary.overdueInstallments > 0 && (
          <View style={styles.summaryRow}>
            <Text style={[styles.summaryLabel, styles.overdue]}>Overdue:</Text>
            <Text style={[styles.summaryValue, styles.overdue]}>{data.summary.overdueInstallments}</Text>
          </View>
        )}
      </View>
      
      <Text style={styles.listTitle}>Installment Details</Text>
      <FlatList
        data={data.installments}
        renderItem={renderInstallmentItem}
        keyExtractor={(item, index) => index.toString()}
        contentContainerStyle={styles.listContainer}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
    backgroundColor: '#f5f5f5',
  },
  centered: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  errorText: {
    color: 'red',
    fontSize: 16,
  },
  summaryContainer: {
    backgroundColor: 'white',
    borderRadius: 8,
    padding: 16,
    marginBottom: 16,
    elevation: 2,
  },
  summaryTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 12,
  },
  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  summaryLabel: {
    fontSize: 16,
    color: '#555',
  },
  summaryValue: {
    fontSize: 16,
    fontWeight: '600',
  },
  listTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  listContainer: {
    paddingBottom: 16,
  },
  installmentItem: {
    backgroundColor: 'white',
    borderRadius: 8,
    padding: 16,
    marginBottom: 12,
    elevation: 2,
  },
  row: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  label: {
    fontSize: 14,
    color: '#555',
  },
  value: {
    fontSize: 14,
    fontWeight: '500',
  },
  paid: {
    color: 'green',
  },
  unpaid: {
    color: 'orange',
  },
  lateWarning: {
    backgroundColor: '#ffebee',
    padding: 8,
    borderRadius: 4,
    marginTop: 8,
  },
  lateText: {
    color: '#d32f2f',
    textAlign: 'center',
  },
  overdue: {
    color: '#d32f2f',
  },
});

export default InstallmentsScreen;