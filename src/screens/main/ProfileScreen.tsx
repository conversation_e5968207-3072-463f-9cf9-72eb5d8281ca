import React, { useCallback } from 'react';
import { View, StyleSheet, ScrollView, TouchableOpacity, Text, Button } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack'; // Changed from NativeStackNavigationProp
import { useAppDispatch, useAppSelector } from '../../store';
import { logout } from '../../store/slices/authSlice';
import { ProfileStackParamList } from '../../navigation/ProfileStack'; // Import the new ParamList
import theme from '../../constants/theme';
import * as Updates from 'expo-updates';

// Update the navigation prop type
type ProfileScreenNavigationProp = StackNavigationProp< // Changed to StackNavigationProp
  ProfileStackParamList,
  'ProfileDetails' // Use the correct route name from ProfileStackParamList
>;

const ProfileScreen = () => {
  const navigation = useNavigation<ProfileScreenNavigationProp>();
  const dispatch = useAppDispatch();
  const user = useAppSelector((state) => state.auth.user);

  const handleLogout = useCallback(() => {
    dispatch(logout());
  }, [dispatch]);

  const getCreditStatusColor = () => {
    switch (user?.creditStatus) {
      case 'approved':
        return '#4CAF50';
      case 'pending':
        return theme.colors.accent;
      case 'rejected':
        return '#F44336';
      default:
        return theme.colors.neutral[60];
    }
  };

  const renderCreditStatus = () => {
    if (!user?.creditStatus) return null;

    return (
      <View style={[styles.section, styles.creditStatus]}>
        <Text style={styles.sectionTitle}>Credit Status</Text>
        <View
          style={[
            styles.statusBadge,
            { backgroundColor: getCreditStatusColor() },
          ]}
        >
          <Text style={styles.statusText}>
            {user.creditStatus.toUpperCase()}
          </Text>
        </View>
      </View>
    );
  };

  const renderProfileSection = () => {
    return (
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Profile</Text>
        <View style={styles.profileInfo}>
          <Text style={styles.label}>Email</Text>
          <Text style={styles.value}>{user?.email}</Text>
        </View>
      </View>
    );
  };

  const renderPreferences = () => {
    return (
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Preferences</Text>
        <TouchableOpacity
          style={styles.menuItem}
          onPress={async () => {
            const i18n = require('../../i18n').default;
            const { I18nManager, Alert } = require('react-native');
            const currentLang = i18n.language || 'ar';
            const nextLang = currentLang.startsWith('ar') ? 'en' : 'ar';
            await i18n.changeLanguage(nextLang);
            const isArabic = nextLang.startsWith('ar');
            if (I18nManager.isRTL !== isArabic) {
              I18nManager.forceRTL(isArabic);
              Alert.alert(
                'Restart Required',
                'The app needs to restart to apply the language direction.',
                [
                  {
                    text: 'OK',
                    onPress: () => {
                      if (typeof Updates !== 'undefined' && Updates.reloadAsync) {
                        Updates.reloadAsync();
                      }
                    }
                  }
                ]
              );
            }
          }}
        >
          <Text style={styles.menuText}>Language</Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={styles.menuItem}
          onPress={() => navigation.navigate('Notifications' as never)}
        >
          <Text style={styles.menuText}>Notifications</Text>
        </TouchableOpacity>
      </View>
    );
  };

  return (
    <ScrollView style={styles.container}>
      {renderProfileSection()}
      {renderCreditStatus()}
      {renderPreferences()}

      <View style={styles.section}>
        <Button
          title="View My Installments"
          onPress={() => navigation.navigate('Installments')} // This should now be type-safe
          color={theme.colors.primary} // Optional: using theme color
        />
      </View>
      
      <TouchableOpacity
        style={styles.logoutButton}
        onPress={handleLogout}
      >
        <Text style={styles.logoutText}>Logout</Text>
      </TouchableOpacity>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.white,
  },
  section: {
    padding: theme.space.md,
    marginVertical: theme.space.sm,
    backgroundColor: theme.colors.white,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.neutral[30],
  },
  creditStatus: {
    marginBottom: theme.space.md,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: theme.colors.neutral[90],
    marginBottom: theme.space.md,
  },
  profileInfo: {
    marginVertical: theme.space.sm,
  },
  label: {
    fontSize: 14,
    color: theme.colors.neutral[60],
    marginBottom: theme.space.xs,
  },
  value: {
    fontSize: 16,
    color: theme.colors.neutral[90],
  },
  statusBadge: {
    alignSelf: 'flex-start',
    paddingHorizontal: theme.space.md,
    paddingVertical: theme.space.xs,
    borderRadius: theme.radius.default,
  },
  statusText: {
    color: theme.colors.white,
    fontWeight: '600',
    fontSize: 12,
  },
  menuItem: {
    paddingVertical: theme.space.md,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.neutral[30],
  },
  menuText: {
    fontSize: 16,
    color: theme.colors.neutral[90],
  },
  // installmentsButton style is not strictly needed if we use the Button's color prop
  // or if default styling is acceptable. Added a general section for it.
  logoutButton: {
    margin: theme.space.md,
    padding: theme.space.md,
    backgroundColor: '#FEE2E2',
    borderRadius: theme.radius.default,
    alignItems: 'center',
  },
  logoutText: {
    color: '#DC2626',
    fontWeight: '600',
    fontSize: 16,
  },
});

export default ProfileScreen;
